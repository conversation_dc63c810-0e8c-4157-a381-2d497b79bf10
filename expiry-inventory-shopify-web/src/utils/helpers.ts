import createApp, {
  AppBridgeState,
  ClientApplication,
} from "@shopify/app-bridge";
import { Redirect, Toast } from "@shopify/app-bridge/actions";
import moment, { Moment } from "moment";
import _store from "@utils/redux/store";
import { setShop } from "@utils/redux/features/shop/shopSlice";
import { API_ENDPOINTS } from "./api/endpoints";
import { getSessionToken } from "@shopify/app-bridge/utilities";
import { PackingSlipRenderItem, Settings } from "@ts-types/types";
import Router from "next/router";
import { ROUTES } from "./routes";
import siteSettings from "@settings/site.settings";
import CryptoJS from "crypto-js";

const app_env = process.env.NEXT_PUBLIC_APP_ENV;

const headers: HeadersInit = {
  Accept: "application/json",
  "Content-Type": "application/json",
};

let appObject: any = null;

export const toastOptions = {
  PRODUCT_NOT_SELECTED: {
    message: "Product/Variant not selected",
    duration: 2000,
    isError: true,
  },
  INVALID_QUANTITY: {
    message: "Quantity is invalid",
    duration: 2000,
    isError: true,
  },
  INVALID_EXPIRY_DATE: {
    message: "Expiry date is invalid",
    duration: 2000,
    isError: true,
  },
  ANALYTICS_EXPORT_FAILED: {
    message: "Dashboard data export failed",
    duration: 2000,
    isError: true,
  },
  ANALYTICS_EXPORT_SUCCESS: {
    message:
      "Dashboard data export will get started soon. You wil get notified by email when export completed.",
    duration: 2000,
  },
  PRODUCT_SAVED: { message: "Product saved", duration: 2000 },
  PRODUCT_CREATED: { message: "Product created", duration: 2000 },
  PRODUCTS_GET_FAILED: {
    message: "Products retrieve failed.",
    duration: 2000,
    isError: true,
  },
  PRODUCT_NOT_FOUND: {
    message: "Products not found.",
    duration: 2000,
    isError: true,
  },
  VARIANTS_GET_FAILED: {
    message: "Product variants retrieve failed.",
    duration: 2000,
    isError: true,
  },
  BATCH_CREATED: { message: "Batch created", duration: 2000 },
  BATCH_NOT_CREATED: {
    message: "Batch creation failed",
    duration: 2000,
    isError: true,
  },
  BATCH_SAVED: { message: "Batch saved", duration: 2000 },
  BATCH_NOT_SAVED: {
    message: "Batch saving failed",
    duration: 2000,
    isError: true,
  },
  BATCH_DELETED: { message: "Batch deleted", duration: 2000 },
  BATCH_NOT_DELETED: {
    message: "Batch deletion failed",
    duration: 2000,
    isError: true,
  },
  BATCH_DELETE_ALL_SUCCESS: {
    message:
      "Batches deletion is successfully requested, the process will run in background",
    duration: 5000,
  },
  BATCH_EXPORT_FAILED: {
    message: "Batch export failed",
    duration: 2000,
    isError: true,
  },
  BATCH_EXPORT_SUCCESS: {
    message:
      "Batch export will get started soon. You wil get notified by email when export completed.",
    duration: 2000,
  },
  PRODUCT_EXPORT_FAILED: {
    message: "Product export failed",
    duration: 2000,
    isError: true,
  },
  PRODUCT_EXPORT_SUCCESS: {
    message:
      "Product export will get started soon. You wil get notified by email when export completed.",
    duration: 2000,
  },
  BATCH_IMPORTED: { message: "Batch imported", duration: 2000 },
  BATCH_IMPORT_FAILED: {
    message: "Batch import failed",
    duration: 2000,
    isError: true,
  },
  BATCH_NOT_FOUND: {
    message: "Batch not found.",
    duration: 2000,
    isError: true,
  },
  BATCH_PRODUCT_LIMIT: {
    message: "Batch products limit exceeded.",
    duration: 2000,
    isError: true,
  },
  BATCH_PRODUCTS_GET_FAILED: {
    message: "Batch products retrieve failed.",
    duration: 2000,
    isError: true,
  },
  BATCH_ORDERS_GET_FAILED: {
    message: "Batch orders retrieve failed.",
    duration: 2000,
    isError: true,
  },
  BATCH_HISTORY_GET_FAILED: {
    message: "Batch history retrieve failed.",
    duration: 2000,
    isError: true,
  },
  ORDER_NOT_FOUND: {
    message: "Order not found",
    duration: 2000,
    isError: true,
  },
  ORDER_AUTO_ASSIGNED: { message: "Order items auto-assigned", duration: 2000 },
  ORDER_AUTO_NOT_ASSIGNED: {
    message: "Order items partially/not assigned",
    duration: 2000,
  },
  ORDERS_AUTO_ASSIGNING: { message: "Auto-assigning batches to orders..." },
  ORDERS_AUTO_ASSIGNED: { message: "Orders batch assigned", duration: 2000 },
  ORDERS_AUTO_NOT_ASSIGNED: {
    message: "Orders batch partially/not assigned",
    duration: 2000,
  },
  ORDERS_ASSIGN_FAILED: {
    message: "Order assign batch failed",
    duration: 2000,
    isError: true,
  },
  ORDER_UNASSIGNED: {
    message: "Order items unassigned from batches",
    duration: 2000,
  },
  ORDERS_UNASSIGN_FAILED: {
    message: "Order unassign batches failed",
    duration: 2000,
    isError: true,
  },
  ORDER_EXPORT_FAILED: {
    message: "Order export failed",
    duration: 2000,
    isError: true,
  },
  ORDER_EXPORT_SUCCESS: {
    message:
      "Order export will get started soon. You wil get notified by email when export completed.",
    duration: 2000,
  },
  ORDERS_GET_FAILED: {
    message: "Orders retrieve failed",
    duration: 2000,
    isError: true,
  },
  LOCATIONS_GET_FAILED: {
    message: "Locations retrieve failed.",
    duration: 2000,
    isError: true,
  },
  PREFERENCES_SAVED: { message: "Settings saved", duration: 2000 },
  PREFERENCES_NOT_SAVED: {
    message: "Settings saving failed",
    duration: 2000,
    isError: true,
  },
  META_PRESETS_SAVED: { message: "Meta presets saved", duration: 2000 },
  PACKING_SLIP_SAVED: {
    message: "Packing slip template saved",
    duration: 2000,
  },
  PACKING_SLIP_NOT_SAVED: {
    message: "Packing slip template saving failed",
    duration: 2000,
    isError: true,
  },
  PACKING_SLIP_RESET: {
    message: "Packing slip template reseted to default",
    duration: 2000,
  },
  PASSWORD_SETUP_SUCCESS: {
    message: "Password is successfully saved",
    duration: 2000,
  },
  PASSWORD_RESET_SUCCESS: {
    message: "Password reset is successful",
    duration: 2000,
  },
  PLAN_UPGRADED: { message: "Plan subscribed", duration: 2000 },
  PLAN_DOWNGRADED: { message: "Plan downgraded", duration: 2000 },
  DISCOUNT_ACTIVATED: { message: "Discount activated", duration: 2000 },
  DISCOUNT_CANCELLED: { message: "Discount cancelled", duration: 2000 },
  BATCH_TRANSFER_SUCCESS: {
    message: "Batch quantity transfer is successful",
    duration: 2000,
  },
};

export const base64Encode = (host: string) => {
  return Buffer.from(host, "base64").toString("ascii");
};

export const getShopURL = () => {
  return Router.query && Router.query.shop
    ? Router.query.shop
    : _store.store.getState().shop._details
    ? _store.store.getState().shop._details.shop_url
    : null;
};

export const getHost = () => {
  const host = Router.query && Router.query.host ? Router.query.host : null;
  return host ? base64Encode(host as string) : null;
};

export const getAppConfig = (host?: string) => {
  const apiKey = process.env.NEXT_PUBLIC_SHOPIFY_KEY!;
  const config = {
    apiKey,
    host: (Router.query && Router.query.host
      ? Router.query.host
      : host) as string,
    forceRedirect: true,
  };
  return config;
};

export const createAppObject = (host?: string) => {
  if (!appObject) {
    appObject = createApp(getAppConfig(host));
  }
  return appObject;
};

export function generateRandomString(length: number) {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

export const formatDate = (
  date_string: string | Date | null | undefined,
  format = "MMM D, YYYY"
) => {
  if (date_string) {
    return moment(date_string).format(format);
  }
  return date_string as string;
};

export const dateDiff = (
  from: string | Date | Moment,
  to: string | Date | Moment,
  unit: moment.unitOfTime.Diff = "days"
) => {
  const fromDate = moment(from);
  const toDate = moment(to);
  return toDate.diff(fromDate, unit);
};

export const showToast = (options: any) => {
  const toast = Toast.create(createAppObject(), options);
  toast.dispatch(Toast.Action.SHOW);
};

export const objectValue = (obj: any, keys: string | string[]) => {
  let value = obj;
  if (keys instanceof Array) {
    keys.forEach((key) => {
      value = value[key];
    });
    return value;
  }
  return value[keys];
};

export const sumArray = (
  arr: any[],
  property: string | string[] | null = null
) => {
  return arr.reduce(
    (total, item) => total + (property ? objectValue(item, property) : item),
    0
  );
};

export const getMerchantSettings = (force = false) => {
  return new Promise<Settings>(async (resolve, reject) => {
    if (force === false) {
      const shop_data = _store.store.getState().shop._details;
      if (shop_data.settings) {
        resolve(shop_data.settings);
      }
    }
    // if(force === false) {
    //     // const { shop } = getShopCredentials();
    //     const shop_data = _store.store.getState().shop._details;
    //     if(shop_data.settings) {
    //         resolve(shop_data.settings);
    //     }
    // }
    // else {
    fetchAPI("GET", API_ENDPOINTS.PREFERENCE)
      .then((response) => {
        _store.store.dispatch(setShop({ settings: response }));
        resolve(response as Settings);
      })
      .catch((error) => {
        reject(error);
      });
    // }
  });
};

export const setMerchantSettings = (settings_data: any, old_settings: any) => {
  return new Promise(async (resolve, reject) => {
    settings_data = {
      // ...(getShopCredentials().shop?.settings),
      ...old_settings,
      ..._store.store.getState().shop._details.settings,
      ...settings_data,
    };
    fetchAPI("PUT", API_ENDPOINTS.PREFERENCE, settings_data)
      .then(() => {
        // setShop({settings: settings_data});
        _store.store.dispatch(setShop({ settings: settings_data }));
        resolve(true);
      })
      .catch(() => {
        resolve(false);
      });
  });
};

export function generateGrantPayloadString(nonce: string) {
  const redirectUrl =
    process.env.NEXT_PUBLIC_APP_URL! + ROUTES.SHOPIFY_CALLBACK;
  const grantPayload = {
    client_id: process.env.NEXT_PUBLIC_SHOPIFY_KEY!,
    scope: siteSettings.shopify.access_scopes.join(","),
    redirect_uri: redirectUrl,
    state: nonce,
    "grant_options[]": "",
  };
  return new URLSearchParams(grantPayload).toString();
}

export const fetchAPIWithShopURL = (
  method: string,
  path: string,
  shop_url: string,
  data?: any
) => {
  return new Promise(async (resolve, reject) => {
    const backend_url = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (method === "GET") {
      if (data) {
        path += "?";
        path += Object.keys(data)
          .map((key) => key + "=" + data[key])
          .join("&");
        data = undefined;
      }
    }

    const request_headers: any = {
      ...headers,
      ...(app_env == "dev"
        ? {
            "ngrok-skip-browser-warning": "1",
            "User-Agent": "Bypass-Ngrok",
          }
        : {}),
    };
    // const shop_url = getShopURL();
    // if(!shop_url) {
    //     reject("Cannot get shop URL");
    //     return;
    // }
    request_headers["X-Shop-URL"] = shop_url;
    const request = new Request(backend_url + path, {
      method,
      body: JSON.stringify(data),
      headers: request_headers,
    });
    fetch(request)
      .then(async (response_data) => {
        const response_json = await response_data.json();
        if (
          !response_json.code_status ||
          (response_json.code_status && response_json.code_status > 200)
        ) {
          reject(response_json);
        }
        resolve(response_json.data);
      })
      .catch((error) => {
        reject({
          message: error,
        });
      });
  });
};

export const fetchAPI = (
  method: string,
  path: string,
  data?: any,
  is_auth = true,
  host?: string
) => {
  return new Promise(async (resolve, reject) => {
    const backend_url = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (method === "GET") {
      if (data) {
        path += "?";
        path += Object.keys(data)
          .map((key) => key + "=" + data[key])
          .join("&");
        data = undefined;
      }
    }
    const request_headers: any = {
      ...headers,
      ...(app_env == "dev"
        ? {
            "ngrok-skip-browser-warning": "1",
            "User-Agent": "Bypass-Ngrok",
          }
        : {}),
    };
    if (is_auth) {
      // if(!host && !_store.store.getState().shop._details.host) {
      //     return reject({ message: "No host provided" });
      // }
      const session_token = await getSessionToken(createAppObject(host));
      request_headers.Authorization = `Bearer ${session_token}`;
    }
    const request = new Request(backend_url + path, {
      method,
      body: JSON.stringify(data),
      headers: request_headers,
    });
    fetch(request)
      .then(async (response_data) => {
        const response_json = await response_data.json();
        if (
          !response_json.code_status ||
          (response_json.code_status && response_json.code_status > 200)
        ) {
          reject(response_json);
        }
        resolve(response_json.data);
      })
      .catch((error) => {
        reject({
          message: error,
        });
      });
  });
};

export const includeQueryIntoUrl = (url: string, options?: any) => {
  if (options) {
    url += "?";
    url += Object.keys(options)
      .map((key) => key + "=" + options[key])
      .join("&");
  }
  return url;
};

export function renderErrors(errors: any): any {
  try {
    if (errors.data) {
      if (typeof errors.data == "string") {
        return errors.data;
      }
      if (Array.isArray(errors.data) && errors.data.length) {
        return renderErrors(errors.data);
      }
      if (
        typeof errors.data == "object" &&
        Object.entries(errors.data).length
      ) {
        return renderErrors(errors.data);
      }
    }
    if (errors.message) {
      return remapValidationErrorMessage(errors.message);
    }
    const arr = Array.isArray(errors) ? errors : Object.entries(errors);
    if (arr.length > 0) {
      const data = arr[0];
      if (typeof data == "string") {
        return remapValidationErrorMessage(data);
      }
      const [type, message] = data;
      if (typeof message == "string") {
        return remapValidationErrorMessage(message);
      }
      return renderErrors(message);
    }
    return renderErrors(arr);
  } catch (e: any) {
    return undefined;
  }
}

export function remapValidationErrorMessage(message: string) {
  switch (message) {
    case "validation.current_password":
      return "Password is incorrect";
  }
  return message;
}

export function handleExternalLink(
  app: ClientApplication<AppBridgeState>,
  path: string
) {
  const redirect = Redirect.create(app);
  redirect.dispatch(Redirect.Action.ADMIN_PATH, {
    path,
    newContext: true,
  });
}

export function generatePackingSlipItemsCipher(
  items: Array<PackingSlipRenderItem>
) {
  if (
    !items.length ||
    items.every(
      ({ checked, assignments }) =>
        checked &&
        assignments.every(({ checked: assignmentChecked }) => assignmentChecked)
    )
  ) {
    return null;
  }
  const itemsString = items
    .filter(({ checked }) => checked)
    .map(({ order_item_id, assignments }) => {
      if (assignments.some(({ checked }) => !checked)) {
        return `${order_item_id};${assignments
          .filter(({ checked }) => checked)
          .map(
            ({ assignment_id, quantity }) =>
              `${!assignment_id ? "N" : assignment_id}:${quantity}`
          )
          .join(",")}`;
      }
      return `${order_item_id}`;
    })
    .join("|");

  const passphrase = process.env.NEXT_PUBLIC_ENC_PASSPHRASE!;
  const key = CryptoJS.enc.Utf8.parse(passphrase);

  const ivlen = 16;
  const iv = CryptoJS.lib.WordArray.random(ivlen);

  const encrypted = CryptoJS.AES.encrypt(itemsString, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  const hmac = CryptoJS.HmacSHA256(encrypted.ciphertext, key);

  return (
    iv.toString(CryptoJS.enc.Hex) +
    hmac.toString(CryptoJS.enc.Hex) +
    encrypted.toString(CryptoJS.format.Hex)
  );
}
