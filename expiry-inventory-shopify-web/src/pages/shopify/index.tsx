import type { GetServerSideProps } from "next";
import React, { useEffect } from "react";
import { useRouter } from "next/router";
import { ROUTES } from "@utils/routes";
import Layout from "@components/layouts/layout";
import HmacSHA256 from "crypto-js/hmac-sha256";
import HexEncode from "crypto-js/enc-hex";
import createApp from "@shopify/app-bridge";
import { Redirect } from "@shopify/app-bridge/actions";
import { fetchAPIWithShopURL, getAppConfig } from "@utils/helpers";
import {
  generateGrantPayloadString,
  generateRandomString,
} from "@utils/helpers";
import { isShopifyEmbedded } from "@shopify/app-bridge/utilities";
import PageLoading from "@components/common/page-loading";
import { useShopify } from "@contexts/shopify.context";
import { API_ENDPOINTS } from "@utils/api/endpoints";
import siteSettings from "@settings/site.settings";

export default function Shopify({}: {}) {
  const router = useRouter();
  const { host } = useShopify();
  useEffect(() => {
    if (isShopifyEmbedded()) {
      if (host) {
        const app = createApp(getAppConfig(host as string));
        const redirect = Redirect.create(app);
        redirect.dispatch(Redirect.Action.APP, ROUTES.DASHBOARD);
      }
    } else {
      router.replace(ROUTES.SHOPIFY_LOGIN);
    }
  }, [host]);

  return <PageLoading />;
}

Shopify.Layout = Layout;

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const { query } = ctx;
  const { shop, host, hmac, embedded, regrant } = query;
  console.log("Shopify", query);
  if (shop) {
    if (embedded === "1") {
      const redirectUriParams = {
        shop,
        host,
        regrant: 0,
      };

      // check access scopes
      try {
        const response: any = await fetchAPIWithShopURL(
          "POST",
          API_ENDPOINTS.SHOPIFY_ACCESS_SCOPES,
          shop as string,
          { access_scopes: siteSettings.shopify.access_scopes }
        );
        if (!response || response.require_oauth) {
          redirectUriParams.regrant = 1;
        }
      } catch (error: any) {
        // If merchant not found (404) or any other error, require OAuth
        console.log("Access scope check failed:", error);
        redirectUriParams.regrant = 1;
      }

      // @ts-ignore
      const redirectUriParamsString = new URLSearchParams(
        redirectUriParams
      ).toString();
      // @ts-ignore
      const queryParams = new URLSearchParams({
        ...query,
        shop,
        redirectUri: `${process.env.NEXT_PUBLIC_APP_URL!}${
          ROUTES.SHOPIFY
        }?${redirectUriParamsString}`,
      }).toString();

      // to exitiframe
      return {
        redirect: {
          permanent: false,
          destination: `${ROUTES.EXIT_IFRAME}?${queryParams}`,
        },
      };
    }

    // performs OAuth and redirection
    if (validateShopURL(shop as string)) {
      if (
        (regrant && regrant == "1") ||
        validateSignature(hmac as string, { ...query })
      ) {
        const nonce = generateRandomString(8);
        ctx.res.setHeader("set-cookie", ["nonce=" + nonce]);
        const queryString = generateGrantPayloadString(nonce);
        return {
          redirect: {
            permanent: false,
            destination: `https://${shop}/admin/oauth/authorize?${queryString}`,
          },
        };
      }
    }

    // @ts-ignore
    const redirectUriParams = new URLSearchParams({
      shop,
      host,
    }).toString();

    return {
      redirect: {
        permanent: false,
        destination: `${ROUTES.DASHBOARD}?${redirectUriParams}`,
      },
    };
  }
  return {
    props: {},
  };
};

function validateShopURL(shop_url: string) {
  const validShopUrlRegex = /[a-zA-Z0-9][a-zA-Z0-9\-]*\.myshopify\.com/;
  return validShopUrlRegex.test(shop_url);
}

function validateSignature(providedSignature: string, hashContent: any) {
  delete hashContent.hmac;
  const hashString = Object.keys(hashContent)
    .sort()
    .map((key) => `${key}=${hashContent[key]}`)
    .join("&");
  const generatedSignature = HmacSHA256(
    hashString,
    process.env.NEXT_PUBLIC_SHOPIFY_SECRET!
  ).toString(HexEncode);
  return generatedSignature === providedSignature;
}
